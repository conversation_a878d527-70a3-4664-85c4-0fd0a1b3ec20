#!/usr/bin/env python3
"""
AutoMem 简化服务器 - 用于测试和快速启动
避免立即加载AI模型，提供基本的MCP服务
"""

import asyncio
import json
import sys
import logging
from pathlib import Path
from typing import Any, Dict

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleAutoMemServer:
    """简化的 AutoMem 服务器"""
    
    def __init__(self):
        self.memories = []  # 简单的内存存储
        self.running = False
        
    async def start(self):
        """启动服务器"""
        self.running = True
        logger.info("🚀 AutoMem 简化服务器启动成功")
        logger.info("📝 提供基本的记忆存储和检索功能")
        
        # 模拟MCP协议响应
        while self.running:
            try:
                # 读取标准输入
                line = await asyncio.get_event_loop().run_in_executor(
                    None, sys.stdin.readline
                )
                
                if not line:
                    break
                    
                # 处理MCP请求
                try:
                    request = json.loads(line.strip())
                    response = await self.handle_request(request)
                    
                    # 输出响应
                    print(json.dumps(response))
                    sys.stdout.flush()
                    
                except json.JSONDecodeError:
                    logger.warning(f"无效的JSON请求: {line}")
                    
            except Exception as e:
                logger.error(f"处理请求时出错: {e}")
                
    async def handle_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理MCP请求"""
        method = request.get("method", "")
        params = request.get("params", {})
        request_id = request.get("id")
        
        try:
            if method == "initialize":
                return {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "result": {
                        "protocolVersion": "2024-11-05",
                        "capabilities": {
                            "tools": {
                                "listChanged": True
                            },
                            "resources": {
                                "subscribe": True,
                                "listChanged": True
                            }
                        },
                        "serverInfo": {
                            "name": "AutoMem-Simple",
                            "version": "0.1.0"
                        }
                    }
                }
                
            elif method == "tools/list":
                return {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "result": {
                        "tools": [
                            {
                                "name": "store_memory",
                                "description": "存储一个记忆",
                                "inputSchema": {
                                    "type": "object",
                                    "properties": {
                                        "content": {
                                            "type": "string",
                                            "description": "记忆内容"
                                        },
                                        "tags": {
                                            "type": "array",
                                            "items": {"type": "string"},
                                            "description": "标签列表"
                                        }
                                    },
                                    "required": ["content"]
                                }
                            },
                            {
                                "name": "search_memories",
                                "description": "搜索记忆",
                                "inputSchema": {
                                    "type": "object",
                                    "properties": {
                                        "query": {
                                            "type": "string",
                                            "description": "搜索查询"
                                        }
                                    },
                                    "required": ["query"]
                                }
                            }
                        ]
                    }
                }
                
            elif method == "tools/call":
                tool_name = params.get("name")
                arguments = params.get("arguments", {})
                
                if tool_name == "store_memory":
                    content = arguments.get("content", "")
                    tags = arguments.get("tags", [])
                    
                    memory = {
                        "id": len(self.memories) + 1,
                        "content": content,
                        "tags": tags,
                        "timestamp": "2024-01-15T10:00:00Z"
                    }
                    self.memories.append(memory)
                    
                    return {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "result": {
                            "content": [
                                {
                                    "type": "text",
                                    "text": f"✅ 记忆已存储！\n内容: {content}\n标签: {', '.join(tags)}\nID: {memory['id']}"
                                }
                            ]
                        }
                    }
                    
                elif tool_name == "search_memories":
                    query = arguments.get("query", "")
                    
                    # 简单的关键词搜索
                    results = []
                    for memory in self.memories:
                        if query.lower() in memory["content"].lower():
                            results.append(memory)
                    
                    if results:
                        result_text = f"🔍 找到 {len(results)} 条相关记忆:\n\n"
                        for i, memory in enumerate(results[:5], 1):
                            result_text += f"{i}. {memory['content'][:100]}...\n"
                            result_text += f"   标签: {', '.join(memory['tags'])}\n\n"
                    else:
                        result_text = "😔 没有找到相关记忆"
                    
                    return {
                        "jsonrpc": "2.0",
                        "id": request_id,
                        "result": {
                            "content": [
                                {
                                    "type": "text",
                                    "text": result_text
                                }
                            ]
                        }
                    }
                    
            # 默认响应
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "error": {
                    "code": -32601,
                    "message": f"方法未找到: {method}"
                }
            }
            
        except Exception as e:
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "error": {
                    "code": -32603,
                    "message": f"内部错误: {str(e)}"
                }
            }
    
    def stop(self):
        """停止服务器"""
        self.running = False
        logger.info("🛑 AutoMem 简化服务器已停止")

async def main():
    """主函数"""
    server = SimpleAutoMemServer()
    try:
        await server.start()
    except KeyboardInterrupt:
        logger.info("收到中断信号")
    finally:
        server.stop()

if __name__ == "__main__":
    asyncio.run(main())
