#!/usr/bin/env python3
"""
AutoMem 超简单启动器 - 绕过所有检查，直接启动
"""

import subprocess
import sys
import os
import tkinter as tk
from tkinter import messagebox
import time

def start_simple_server():
    """启动简化服务器"""
    try:
        print("?? 启动 AutoMem 简化服务器...")
        
        # 直接启动简化服务器
        process = subprocess.Popen(
            [sys.executable, "simple_server.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.getcwd()
        )
        
        # 等待一下确保启动
        time.sleep(2)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("? AutoMem 简化服务器启动成功！")
            print("?? 服务器正在运行，提供基本的记忆存储和检索功能")
            print("?? 可以配置到 Claude Desktop 中使用")
            print("\n按 Ctrl+C 停止服务器")
            
            # 等待进程结束
            try:
                process.wait()
            except KeyboardInterrupt:
                print("\n?? 正在停止服务器...")
                process.terminate()
                process.wait()
                print("? 服务器已停止")
        else:
            # 读取错误信息
            stdout, stderr = process.communicate()
            print(f"? 启动失败")
            if stderr:
                print(f"错误信息: {stderr}")
            if stdout:
                print(f"输出信息: {stdout}")
                
    except Exception as e:
        print(f"? 启动失败: {e}")

def show_gui_choice():
    """显示启动选择界面"""
    root = tk.Tk()
    root.title("AutoMem 启动器")
    root.geometry("400x300")
    root.resizable(False, False)
    
    # 标题
    title_label = tk.Label(root, text="?? AutoMem 智能记忆管理系统", 
                          font=("Arial", 16, "bold"))
    title_label.pack(pady=20)
    
    # 说明
    info_label = tk.Label(root, text="选择启动方式：", font=("Arial", 12))
    info_label.pack(pady=10)
    
    # 按钮框架
    button_frame = tk.Frame(root)
    button_frame.pack(pady=20)
    
    def start_simple():
        root.destroy()
        start_simple_server()
    
    def start_gui():
        root.destroy()
        try:
            subprocess.run([sys.executable, "automem_manager.py"], check=True)
        except Exception as e:
            messagebox.showerror("错误", f"启动 GUI 失败: {e}")
    
    def show_config():
        config_text = """
配置 Claude Desktop:

1. 打开文件: %APPDATA%\\Claude\\claude_desktop_config.json

2. 添加以下内容:
{
  "mcpServers": {
    "automem-simple": {
      "command": "python",
      "args": ["simple_server.py"],
      "cwd": "D:\\\\Users\\\\Administrator\\\\Desktop\\\\项目\\\\AutoMem"
    }
  }
}

3. 重启 Claude Desktop

4. 在 Claude 中测试:
   "请帮我存储一个记忆：测试 AutoMem 系统"
        """
        messagebox.showinfo("配置说明", config_text)
    
    # 按钮
    simple_btn = tk.Button(button_frame, text="?? 启动简化服务器\n(推荐，快速启动)", 
                          command=start_simple, width=20, height=3,
                          bg="#4CAF50", fg="white", font=("Arial", 10, "bold"))
    simple_btn.pack(pady=5)
    
    gui_btn = tk.Button(button_frame, text="??? 启动 GUI 管理器\n(图形界面)", 
                       command=start_gui, width=20, height=3,
                       bg="#2196F3", fg="white", font=("Arial", 10, "bold"))
    gui_btn.pack(pady=5)
    
    config_btn = tk.Button(button_frame, text="?? 查看配置说明\n(Claude Desktop)", 
                          command=show_config, width=20, height=3,
                          bg="#FF9800", fg="white", font=("Arial", 10, "bold"))
    config_btn.pack(pady=5)
    
    quit_btn = tk.Button(button_frame, text="? 退出", 
                        command=root.quit, width=20, height=2,
                        bg="#f44336", fg="white", font=("Arial", 10, "bold"))
    quit_btn.pack(pady=5)
    
    # 状态信息
    status_label = tk.Label(root, text="? 系统就绪，选择启动方式", 
                           font=("Arial", 10), fg="green")
    status_label.pack(pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--server":
        # 直接启动服务器
        start_simple_server()
    else:
        # 显示选择界面
        show_gui_choice()
