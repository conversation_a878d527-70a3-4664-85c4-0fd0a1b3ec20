@echo off
echo ========================================
echo    AutoMem 智能记忆管理系统
echo    快速启动脚本
echo ========================================
echo.

echo 1. 启动 GUI 管理器
echo 2. 启动简化服务器
echo 3. 配置 Claude Desktop
echo 4. 退出
echo.

set /p choice="请选择 (1-4): "

if "%choice%"=="1" (
    echo 正在启动 GUI 管理器...
    python start_gui.py
) else if "%choice%"=="2" (
    echo 正在启动简化服务器...
    echo 按 Ctrl+C 停止服务器
    python simple_server.py
) else if "%choice%"=="3" (
    echo.
    echo 配置 Claude Desktop:
    echo 1. 复制 claude_desktop_config.json 的内容
    echo 2. 打开 Claude Desktop 配置文件:
    echo    %APPDATA%\Claude\claude_desktop_config.json
    echo 3. 将内容粘贴到配置文件中
    echo 4. 重启 Claude Desktop
    echo.
    echo 配置文件内容:
    type claude_desktop_config.json
    echo.
    pause
) else if "%choice%"=="4" (
    echo 再见！
    exit
) else (
    echo 无效选择，请重试
    pause
    goto start
)

pause
