#!/usr/bin/env python3
"""
AutoMem GUI 管理工具
Windows 专用图形界面管理器
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import customtkinter as ctk
import threading
import subprocess
import sys
import os
import json
import yaml
import psutil
from pathlib import Path
import webbrowser
from datetime import datetime

# 设置 CustomTkinter 主题
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class AutoMemManager:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("AutoMem 管理器 v1.0")
        self.root.geometry("1200x800")
        self.root.iconbitmap("assets/icon.ico") if os.path.exists("assets/icon.ico") else None
        
        # 状态变量
        self.service_running = False
        self.service_process = None
        self.config_file = "config.yaml"
        self.data_dir = "data"
        
        # 创建界面
        self.create_widgets()
        self.load_config()
        self.update_status()
        
    def create_widgets(self):
        """创建主界面"""
        # 主容器
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ctk.CTkLabel(
            self.main_frame, 
            text="AutoMem 智能记忆管理系统",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # 创建选项卡
        self.tabview = ctk.CTkTabview(self.main_frame)
        self.tabview.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 添加选项卡
        self.tab_dashboard = self.tabview.add("仪表板")
        self.tab_config = self.tabview.add("配置")
        self.tab_logs = self.tabview.add("日志")
        self.tab_tools = self.tabview.add("工具")
        self.tab_about = self.tabview.add("关于")
        
        self.create_dashboard_tab()
        self.create_config_tab()
        self.create_logs_tab()
        self.create_tools_tab()
        self.create_about_tab()
        
    def create_dashboard_tab(self):
        """创建仪表板选项卡"""
        # 状态区域
        status_frame = ctk.CTkFrame(self.tab_dashboard)
        status_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(status_frame, text="服务状态", font=ctk.CTkFont(size=18, weight="bold")).pack(pady=10)
        
        # 状态指示器
        self.status_frame = ctk.CTkFrame(status_frame)
        self.status_frame.pack(fill="x", padx=20, pady=10)
        
        self.status_label = ctk.CTkLabel(self.status_frame, text="● 未运行", text_color="red")
        self.status_label.pack(side="left", padx=10)
        
        self.status_detail = ctk.CTkLabel(self.status_frame, text="AutoMem 服务未启动")
        self.status_detail.pack(side="left", padx=10)
        
        # 控制按钮
        button_frame = ctk.CTkFrame(status_frame)
        button_frame.pack(fill="x", padx=20, pady=10)
        
        self.start_button = ctk.CTkButton(
            button_frame, 
            text="启动服务", 
            command=self.start_service,
            fg_color="green",
            hover_color="darkgreen"
        )
        self.start_button.pack(side="left", padx=10)
        
        self.stop_button = ctk.CTkButton(
            button_frame, 
            text="停止服务", 
            command=self.stop_service,
            fg_color="red",
            hover_color="darkred",
            state="disabled"
        )
        self.stop_button.pack(side="left", padx=10)
        
        self.restart_button = ctk.CTkButton(
            button_frame, 
            text="重启服务", 
            command=self.restart_service,
            fg_color="orange",
            hover_color="darkorange"
        )
        self.restart_button.pack(side="left", padx=10)
        
        # 快速设置
        quick_frame = ctk.CTkFrame(self.tab_dashboard)
        quick_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(quick_frame, text="快速设置", font=ctk.CTkFont(size=18, weight="bold")).pack(pady=10)
        
        quick_buttons_frame = ctk.CTkFrame(quick_frame)
        quick_buttons_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkButton(quick_buttons_frame, text="打开配置文件", command=self.open_config_file).pack(side="left", padx=10)
        ctk.CTkButton(quick_buttons_frame, text="打开数据目录", command=self.open_data_dir).pack(side="left", padx=10)
        ctk.CTkButton(quick_buttons_frame, text="查看日志", command=self.view_logs).pack(side="left", padx=10)
        
        # 系统信息
        info_frame = ctk.CTkFrame(self.tab_dashboard)
        info_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        ctk.CTkLabel(info_frame, text="系统信息", font=ctk.CTkFont(size=18, weight="bold")).pack(pady=10)
        
        self.info_text = ctk.CTkTextbox(info_frame, height=200)
        self.info_text.pack(fill="both", expand=True, padx=20, pady=10)
        
    def create_config_tab(self):
        """创建配置选项卡"""
        # 配置文件选择
        file_frame = ctk.CTkFrame(self.tab_config)
        file_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(file_frame, text="配置文件", font=ctk.CTkFont(size=18, weight="bold")).pack(pady=10)
        
        file_select_frame = ctk.CTkFrame(file_frame)
        file_select_frame.pack(fill="x", padx=20, pady=10)
        
        self.config_path_var = tk.StringVar(value=self.config_file)
        self.config_path_entry = ctk.CTkEntry(file_select_frame, textvariable=self.config_path_var, width=400)
        self.config_path_entry.pack(side="left", padx=10)
        
        ctk.CTkButton(file_select_frame, text="浏览", command=self.browse_config_file).pack(side="left", padx=10)
        ctk.CTkButton(file_select_frame, text="重新加载", command=self.load_config).pack(side="left", padx=10)
        
        # 配置编辑器
        editor_frame = ctk.CTkFrame(self.tab_config)
        editor_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        ctk.CTkLabel(editor_frame, text="配置编辑器", font=ctk.CTkFont(size=18, weight="bold")).pack(pady=10)
        
        self.config_editor = ctk.CTkTextbox(editor_frame, height=400)
        self.config_editor.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 配置按钮
        config_buttons_frame = ctk.CTkFrame(editor_frame)
        config_buttons_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkButton(config_buttons_frame, text="保存配置", command=self.save_config).pack(side="left", padx=10)
        ctk.CTkButton(config_buttons_frame, text="重置配置", command=self.reset_config).pack(side="left", padx=10)
        ctk.CTkButton(config_buttons_frame, text="验证配置", command=self.validate_config).pack(side="left", padx=10)
        
    def create_logs_tab(self):
        """创建日志选项卡"""
        # 日志控制
        log_control_frame = ctk.CTkFrame(self.tab_logs)
        log_control_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(log_control_frame, text="日志查看器", font=ctk.CTkFont(size=18, weight="bold")).pack(pady=10)
        
        log_buttons_frame = ctk.CTkFrame(log_control_frame)
        log_buttons_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkButton(log_buttons_frame, text="刷新日志", command=self.refresh_logs).pack(side="left", padx=10)
        ctk.CTkButton(log_buttons_frame, text="清空日志", command=self.clear_logs).pack(side="left", padx=10)
        ctk.CTkButton(log_buttons_frame, text="导出日志", command=self.export_logs).pack(side="left", padx=10)
        
        # 日志级别选择
        self.log_level_var = tk.StringVar(value="INFO")
        log_level_menu = ctk.CTkOptionMenu(
            log_buttons_frame, 
            values=["DEBUG", "INFO", "WARNING", "ERROR"],
            variable=self.log_level_var,
            command=self.change_log_level
        )
        log_level_menu.pack(side="left", padx=10)
        
        # 日志显示
        log_display_frame = ctk.CTkFrame(self.tab_logs)
        log_display_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        self.log_text = ctk.CTkTextbox(log_display_frame, height=500)
        self.log_text.pack(fill="both", expand=True, padx=20, pady=10)
        
    def create_tools_tab(self):
        """创建工具选项卡"""
        # 数据管理
        data_frame = ctk.CTkFrame(self.tab_tools)
        data_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(data_frame, text="数据管理", font=ctk.CTkFont(size=18, weight="bold")).pack(pady=10)
        
        data_buttons_frame = ctk.CTkFrame(data_frame)
        data_buttons_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkButton(data_buttons_frame, text="备份数据", command=self.backup_data).pack(side="left", padx=10)
        ctk.CTkButton(data_buttons_frame, text="恢复数据", command=self.restore_data).pack(side="left", padx=10)
        ctk.CTkButton(data_buttons_frame, text="清理数据", command=self.cleanup_data).pack(side="left", padx=10)
        
        # 系统工具
        system_frame = ctk.CTkFrame(self.tab_tools)
        system_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(system_frame, text="系统工具", font=ctk.CTkFont(size=18, weight="bold")).pack(pady=10)
        
        system_buttons_frame = ctk.CTkFrame(system_frame)
        system_buttons_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkButton(system_buttons_frame, text="检查更新", command=self.check_updates).pack(side="left", padx=10)
        ctk.CTkButton(system_buttons_frame, text="重建索引", command=self.rebuild_index).pack(side="left", padx=10)
        ctk.CTkButton(system_buttons_frame, text="系统诊断", command=self.system_diagnosis).pack(side="left", padx=10)
        
        # Claude Desktop 集成
        claude_frame = ctk.CTkFrame(self.tab_tools)
        claude_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(claude_frame, text="Claude Desktop 集成", font=ctk.CTkFont(size=18, weight="bold")).pack(pady=10)
        
        claude_buttons_frame = ctk.CTkFrame(claude_frame)
        claude_buttons_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkButton(claude_buttons_frame, text="生成配置", command=self.generate_claude_config).pack(side="left", padx=10)
        ctk.CTkButton(claude_buttons_frame, text="打开配置文件", command=self.open_claude_config).pack(side="left", padx=10)
        ctk.CTkButton(claude_buttons_frame, text="测试连接", command=self.test_claude_connection).pack(side="left", padx=10)
        
        # 工具输出
        output_frame = ctk.CTkFrame(self.tab_tools)
        output_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        ctk.CTkLabel(output_frame, text="工具输出", font=ctk.CTkFont(size=18, weight="bold")).pack(pady=10)
        
        self.tool_output = ctk.CTkTextbox(output_frame, height=300)
        self.tool_output.pack(fill="both", expand=True, padx=20, pady=10)
        
    def create_about_tab(self):
        """创建关于选项卡"""
        about_frame = ctk.CTkFrame(self.tab_about)
        about_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Logo 和标题
        title_frame = ctk.CTkFrame(about_frame)
        title_frame.pack(fill="x", padx=20, pady=20)
        
        ctk.CTkLabel(
            title_frame, 
            text="AutoMem 智能记忆管理系统",
            font=ctk.CTkFont(size=28, weight="bold")
        ).pack(pady=10)
        
        ctk.CTkLabel(
            title_frame, 
            text="版本 1.0.0",
            font=ctk.CTkFont(size=16)
        ).pack(pady=5)
        
        # 描述
        desc_frame = ctk.CTkFrame(about_frame)
        desc_frame.pack(fill="x", padx=20, pady=10)
        
        description = """
AutoMem 是一个基于 MCP (Model Context Protocol) 的智能记忆管理系统，
为 AI 助手提供持久化记忆能力，通过智能分类、语义搜索和上下文感知等功能，
显著增强 AI 的对话连续性和个性化体验。

主要特性：
• 智能记忆分类和标签生成
• 语义搜索和上下文检索
• 自动重要性评估
• MCP 协议完整支持
• 现代化 GUI 管理界面
        """
        
        ctk.CTkLabel(desc_frame, text=description, justify="left").pack(pady=20)
        
        # 链接按钮
        links_frame = ctk.CTkFrame(about_frame)
        links_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkButton(links_frame, text="GitHub 仓库", command=lambda: webbrowser.open("https://github.com/your-org/automem")).pack(side="left", padx=10)
        ctk.CTkButton(links_frame, text="使用文档", command=lambda: webbrowser.open("https://automem.readthedocs.io")).pack(side="left", padx=10)
        ctk.CTkButton(links_frame, text="问题反馈", command=lambda: webbrowser.open("https://github.com/your-org/automem/issues")).pack(side="left", padx=10)
        
        # 版权信息
        copyright_frame = ctk.CTkFrame(about_frame)
        copyright_frame.pack(fill="x", padx=20, pady=20)
        
        ctk.CTkLabel(
            copyright_frame,
            text="© 2024 AutoMem Team. All rights reserved.\nMIT License",
            font=ctk.CTkFont(size=12)
        ).pack(pady=10)

    # ==================== 核心功能方法 ====================

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_content = f.read()
                    self.config_editor.delete("1.0", tk.END)
                    self.config_editor.insert("1.0", config_content)
            else:
                # 创建默认配置
                self.create_default_config()
        except Exception as e:
            messagebox.showerror("错误", f"加载配置文件失败: {e}")

    def create_default_config(self):
        """创建默认配置文件"""
        default_config = """# AutoMem 配置文件
server_name: "AutoMem-Windows"
debug: false

# 存储配置
storage:
  data_dir: "data"
  max_memories: 50000
  backup_enabled: true
  backup_interval_hours: 24

# 智能处理
intelligence:
  embedding_model: "all-MiniLM-L6-v2"
  auto_classification: true
  auto_tag_enabled: true
  importance_threshold: 0.3

# 日志配置
logging:
  level: "INFO"
  enable_console: true
  enable_file: true
  file_path: "data/logs/automem.log"
  max_file_size: "50MB"
  backup_count: 5

# MCP 配置
mcp:
  server_name: "automem"
  version: "1.0.0"
"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                f.write(default_config)
            self.config_editor.delete("1.0", tk.END)
            self.config_editor.insert("1.0", default_config)
            messagebox.showinfo("成功", "已创建默认配置文件")
        except Exception as e:
            messagebox.showerror("错误", f"创建配置文件失败: {e}")

    def save_config(self):
        """保存配置文件"""
        try:
            config_content = self.config_editor.get("1.0", tk.END)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                f.write(config_content)
            messagebox.showinfo("成功", "配置文件已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置文件失败: {e}")

    def validate_config(self):
        """验证配置文件"""
        try:
            config_content = self.config_editor.get("1.0", tk.END)
            yaml.safe_load(config_content)
            messagebox.showinfo("成功", "配置文件格式正确")
        except yaml.YAMLError as e:
            messagebox.showerror("错误", f"配置文件格式错误: {e}")
        except Exception as e:
            messagebox.showerror("错误", f"验证配置文件失败: {e}")

    def reset_config(self):
        """重置配置文件"""
        if messagebox.askyesno("确认", "确定要重置配置文件吗？这将丢失所有自定义设置。"):
            self.create_default_config()

    def browse_config_file(self):
        """浏览配置文件"""
        filename = filedialog.askopenfilename(
            title="选择配置文件",
            filetypes=[("YAML files", "*.yaml"), ("YAML files", "*.yml"), ("All files", "*.*")]
        )
        if filename:
            self.config_file = filename
            self.config_path_var.set(filename)
            self.load_config()

    # ==================== 服务管理方法 ====================

    def start_service(self):
        """启动 AutoMem 服务"""
        try:
            if self.service_running:
                messagebox.showwarning("警告", "服务已在运行中")
                return

            # 跳过环境检查，直接启动
            # if not self.check_python_env():
            #     return

            # 询问启动模式
            mode = messagebox.askyesnocancel(
                "启动模式选择",
                "选择启动模式：\n\n"
                "是 - 完整模式（包含AI功能，首次启动较慢）\n"
                "否 - 简化模式（快速启动，基本功能）\n"
                "取消 - 取消启动"
            )

            if mode is None:  # 取消
                return
            elif mode:  # 完整模式
                cmd = [sys.executable, "-m", "automem.cli", "serve", "--stdio", "--config", self.config_file]
                startup_msg = "正在启动完整模式（首次启动可能需要下载AI模型）..."
            else:  # 简化模式
                cmd = [sys.executable, "simple_server.py"]
                startup_msg = "正在启动简化模式..."

            # 显示启动进度
            progress_window = tk.Toplevel(self.root)
            progress_window.title("启动服务")
            progress_window.geometry("400x120")
            progress_window.transient(self.root)
            progress_window.grab_set()

            progress_label = ctk.CTkLabel(progress_window, text=startup_msg)
            progress_label.pack(pady=20)

            progress_bar = ctk.CTkProgressBar(progress_window)
            progress_bar.pack(pady=10)
            progress_bar.set(0.5)
            progress_window.update()

            # 启动服务
            self.service_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=os.getcwd()
            )

            # 等待一下确保启动
            import time
            time.sleep(2)
            progress_window.destroy()

            self.service_running = True
            self.update_service_buttons()
            self.update_status()

            # 启动日志监控线程
            threading.Thread(target=self.monitor_service, daemon=True).start()

            messagebox.showinfo("成功", "AutoMem 服务已启动")

        except Exception as e:
            messagebox.showerror("错误", f"启动服务失败: {e}")

    def stop_service(self):
        """停止 AutoMem 服务"""
        try:
            if not self.service_running:
                messagebox.showwarning("警告", "服务未运行")
                return

            if self.service_process:
                self.service_process.terminate()
                self.service_process.wait(timeout=10)
                self.service_process = None

            self.service_running = False
            self.update_service_buttons()
            self.update_status()

            messagebox.showinfo("成功", "AutoMem 服务已停止")

        except subprocess.TimeoutExpired:
            # 强制终止
            if self.service_process:
                self.service_process.kill()
                self.service_process = None
            self.service_running = False
            self.update_service_buttons()
            messagebox.showwarning("警告", "服务已强制停止")
        except Exception as e:
            messagebox.showerror("错误", f"停止服务失败: {e}")

    def restart_service(self):
        """重启 AutoMem 服务"""
        self.stop_service()
        # 等待一秒确保服务完全停止
        self.root.after(1000, self.start_service)

    def monitor_service(self):
        """监控服务状态"""
        while self.service_running and self.service_process:
            try:
                # 检查进程是否还在运行
                if self.service_process.poll() is not None:
                    # 进程已结束
                    self.service_running = False
                    self.root.after(0, self.update_service_buttons)
                    self.root.after(0, self.update_status)
                    break

                # 读取输出日志
                if self.service_process.stdout:
                    line = self.service_process.stdout.readline()
                    if line:
                        self.root.after(0, lambda: self.append_log(line.strip()))

            except Exception:
                break

            # 等待一秒再检查
            threading.Event().wait(1)

    def update_service_buttons(self):
        """更新服务控制按钮状态"""
        if self.service_running:
            self.start_button.configure(state="disabled")
            self.stop_button.configure(state="normal")
        else:
            self.start_button.configure(state="normal")
            self.stop_button.configure(state="disabled")

    def check_python_env(self):
        """检查 Python 环境"""
        try:
            # 显示检查进度
            progress_window = tk.Toplevel(self.root)
            progress_window.title("环境检查")
            progress_window.geometry("350x120")
            progress_window.transient(self.root)
            progress_window.grab_set()

            progress_label = ctk.CTkLabel(progress_window, text="正在检查 Python 环境...")
            progress_label.pack(pady=10)

            progress_bar = ctk.CTkProgressBar(progress_window)
            progress_bar.pack(pady=5)
            progress_bar.set(0.3)
            progress_window.update()

            # 第一步：检查基本模块导入（不加载AI模型）
            progress_label.configure(text="检查基本模块...")
            progress_bar.set(0.6)
            progress_window.update()

            result = subprocess.run(
                [sys.executable, "-c", "import sys; print('Python OK'); import pydantic; print('Pydantic OK'); print('SUCCESS')"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode != 0:
                progress_window.destroy()
                error_msg = f"基础环境检查失败。\n错误信息：{result.stderr}\n\n请检查 Python 和基础依赖包。"
                messagebox.showerror("环境错误", error_msg)
                return False

            # 第二步：检查 AutoMem 模块结构（不初始化AI组件）
            progress_label.configure(text="检查 AutoMem 模块...")
            progress_bar.set(0.9)
            progress_window.update()

            result = subprocess.run(
                [sys.executable, "-c", "from automem.config.settings import AutoMemConfig; print('AutoMem Config OK')"],
                capture_output=True,
                text=True,
                timeout=15
            )

            progress_window.destroy()

            if result.returncode != 0:
                error_msg = f"AutoMem 模块检查失败。\n错误信息：{result.stderr}\n\n模块可能未正确安装。"
                messagebox.showerror("环境错误", error_msg)
                return False

            return True

        except subprocess.TimeoutExpired:
            if 'progress_window' in locals():
                progress_window.destroy()
            messagebox.showerror("错误", "环境检查超时\n请检查系统性能或重试")
            return False
        except Exception as e:
            if 'progress_window' in locals():
                progress_window.destroy()
            messagebox.showerror("错误", f"环境检查失败: {e}")
            return False

    # ==================== 状态更新方法 ====================

    def update_status(self):
        """更新状态显示"""
        if self.service_running:
            self.status_label.configure(text="● 运行中", text_color="green")
            self.status_detail.configure(text="AutoMem 服务正在运行")
        else:
            self.status_label.configure(text="● 未运行", text_color="red")
            self.status_detail.configure(text="AutoMem 服务未启动")

        # 更新系统信息
        self.update_system_info()

        # 定时更新
        self.root.after(5000, self.update_status)

    def update_system_info(self):
        """更新系统信息"""
        try:
            info = []
            info.append(f"系统时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            info.append(f"Python 版本: {sys.version.split()[0]}")
            info.append(f"工作目录: {os.getcwd()}")
            info.append(f"配置文件: {self.config_file}")
            info.append(f"数据目录: {self.data_dir}")

            # 内存使用情况
            process = psutil.Process()
            memory_info = process.memory_info()
            info.append(f"内存使用: {memory_info.rss / 1024 / 1024:.1f} MB")

            # CPU 使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            info.append(f"CPU 使用率: {cpu_percent:.1f}%")

            # 磁盘空间
            disk_usage = psutil.disk_usage('.')
            free_gb = disk_usage.free / 1024 / 1024 / 1024
            info.append(f"可用磁盘空间: {free_gb:.1f} GB")

            # 检查数据目录大小
            if os.path.exists(self.data_dir):
                data_size = self.get_directory_size(self.data_dir)
                info.append(f"数据目录大小: {data_size / 1024 / 1024:.1f} MB")

            self.info_text.delete("1.0", tk.END)
            self.info_text.insert("1.0", "\n".join(info))

        except Exception as e:
            self.info_text.delete("1.0", tk.END)
            self.info_text.insert("1.0", f"获取系统信息失败: {e}")

    def get_directory_size(self, path):
        """获取目录大小"""
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                try:
                    total_size += os.path.getsize(filepath)
                except OSError:
                    pass
        return total_size

    # ==================== 日志管理方法 ====================

    def refresh_logs(self):
        """刷新日志显示"""
        try:
            log_file = os.path.join(self.data_dir, "logs", "automem.log")
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    # 读取最后1000行
                    lines = f.readlines()
                    recent_lines = lines[-1000:] if len(lines) > 1000 else lines

                self.log_text.delete("1.0", tk.END)
                self.log_text.insert("1.0", "".join(recent_lines))

                # 滚动到底部
                self.log_text.see(tk.END)
            else:
                self.log_text.delete("1.0", tk.END)
                self.log_text.insert("1.0", "日志文件不存在")

        except Exception as e:
            messagebox.showerror("错误", f"刷新日志失败: {e}")

    def append_log(self, message):
        """追加日志消息"""
        try:
            self.log_text.insert(tk.END, f"{datetime.now().strftime('%H:%M:%S')} - {message}\n")
            self.log_text.see(tk.END)
        except Exception:
            pass

    def clear_logs(self):
        """清空日志"""
        if messagebox.askyesno("确认", "确定要清空日志吗？"):
            try:
                log_file = os.path.join(self.data_dir, "logs", "automem.log")
                if os.path.exists(log_file):
                    open(log_file, 'w').close()
                self.log_text.delete("1.0", tk.END)
                messagebox.showinfo("成功", "日志已清空")
            except Exception as e:
                messagebox.showerror("错误", f"清空日志失败: {e}")

    def export_logs(self):
        """导出日志"""
        try:
            filename = filedialog.asksaveasfilename(
                title="导出日志",
                defaultextension=".log",
                filetypes=[("Log files", "*.log"), ("Text files", "*.txt"), ("All files", "*.*")]
            )
            if filename:
                log_content = self.log_text.get("1.0", tk.END)
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(log_content)
                messagebox.showinfo("成功", f"日志已导出到: {filename}")
        except Exception as e:
            messagebox.showerror("错误", f"导出日志失败: {e}")

    def change_log_level(self, level):
        """更改日志级别"""
        try:
            # 这里可以实现动态更改日志级别的逻辑
            self.append_log(f"日志级别已更改为: {level}")
        except Exception as e:
            messagebox.showerror("错误", f"更改日志级别失败: {e}")

    def view_logs(self):
        """切换到日志选项卡"""
        self.tabview.set("日志")
        self.refresh_logs()

    # ==================== 文件操作方法 ====================

    def open_config_file(self):
        """打开配置文件"""
        try:
            if os.path.exists(self.config_file):
                os.startfile(self.config_file)
            else:
                messagebox.showwarning("警告", "配置文件不存在")
        except Exception as e:
            messagebox.showerror("错误", f"打开配置文件失败: {e}")

    def open_data_dir(self):
        """打开数据目录"""
        try:
            if os.path.exists(self.data_dir):
                os.startfile(self.data_dir)
            else:
                os.makedirs(self.data_dir, exist_ok=True)
                os.startfile(self.data_dir)
        except Exception as e:
            messagebox.showerror("错误", f"打开数据目录失败: {e}")

    # ==================== 工具方法 ====================

    def backup_data(self):
        """备份数据"""
        try:
            if not os.path.exists(self.data_dir):
                messagebox.showwarning("警告", "数据目录不存在")
                return

            filename = filedialog.asksaveasfilename(
                title="备份数据",
                defaultextension=".zip",
                filetypes=[("ZIP files", "*.zip"), ("All files", "*.*")],
                initialname=f"automem_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
            )

            if filename:
                import zipfile
                with zipfile.ZipFile(filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for root, dirs, files in os.walk(self.data_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arcname = os.path.relpath(file_path, self.data_dir)
                            zipf.write(file_path, arcname)

                self.tool_output.insert(tk.END, f"数据备份完成: {filename}\n")
                messagebox.showinfo("成功", f"数据已备份到: {filename}")

        except Exception as e:
            error_msg = f"备份数据失败: {e}"
            self.tool_output.insert(tk.END, f"{error_msg}\n")
            messagebox.showerror("错误", error_msg)

    def restore_data(self):
        """恢复数据"""
        try:
            filename = filedialog.askopenfilename(
                title="选择备份文件",
                filetypes=[("ZIP files", "*.zip"), ("All files", "*.*")]
            )

            if filename:
                if messagebox.askyesno("确认", "恢复数据将覆盖现有数据，确定继续吗？"):
                    import zipfile
                    with zipfile.ZipFile(filename, 'r') as zipf:
                        zipf.extractall(self.data_dir)

                    self.tool_output.insert(tk.END, f"数据恢复完成: {filename}\n")
                    messagebox.showinfo("成功", "数据恢复完成")

        except Exception as e:
            error_msg = f"恢复数据失败: {e}"
            self.tool_output.insert(tk.END, f"{error_msg}\n")
            messagebox.showerror("错误", error_msg)

    def cleanup_data(self):
        """清理数据"""
        try:
            if messagebox.askyesno("确认", "确定要清理旧数据吗？这将删除30天前的记忆数据。"):
                # 这里可以调用 AutoMem 的清理功能
                cmd = [sys.executable, "-m", "automem.cli", "cleanup", "--days", "30", "--dry-run"]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

                self.tool_output.insert(tk.END, f"清理预览:\n{result.stdout}\n")

                if messagebox.askyesno("确认", "确定要执行清理操作吗？"):
                    cmd = [sys.executable, "-m", "automem.cli", "cleanup", "--days", "30"]
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

                    self.tool_output.insert(tk.END, f"清理完成:\n{result.stdout}\n")
                    messagebox.showinfo("成功", "数据清理完成")

        except subprocess.TimeoutExpired:
            messagebox.showerror("错误", "清理操作超时")
        except Exception as e:
            error_msg = f"清理数据失败: {e}"
            self.tool_output.insert(tk.END, f"{error_msg}\n")
            messagebox.showerror("错误", error_msg)

    def check_updates(self):
        """检查更新"""
        try:
            self.tool_output.insert(tk.END, "正在检查更新...\n")

            # 这里可以实现检查更新的逻辑
            # 例如检查 GitHub releases
            import urllib.request
            import json

            url = "https://api.github.com/repos/your-org/automem/releases/latest"
            with urllib.request.urlopen(url, timeout=10) as response:
                data = json.loads(response.read())
                latest_version = data['tag_name']
                current_version = "v1.0.0"  # 从配置或代码中获取当前版本

                if latest_version != current_version:
                    self.tool_output.insert(tk.END, f"发现新版本: {latest_version}\n")
                    if messagebox.askyesno("更新", f"发现新版本 {latest_version}，是否打开下载页面？"):
                        webbrowser.open(data['html_url'])
                else:
                    self.tool_output.insert(tk.END, "当前已是最新版本\n")
                    messagebox.showinfo("信息", "当前已是最新版本")

        except Exception as e:
            error_msg = f"检查更新失败: {e}"
            self.tool_output.insert(tk.END, f"{error_msg}\n")
            messagebox.showerror("错误", error_msg)

    def rebuild_index(self):
        """重建索引"""
        try:
            if messagebox.askyesno("确认", "重建索引可能需要较长时间，确定继续吗？"):
                self.tool_output.insert(tk.END, "正在重建索引...\n")

                cmd = [sys.executable, "-m", "automem.cli", "rebuild-index"]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

                self.tool_output.insert(tk.END, f"重建索引完成:\n{result.stdout}\n")
                if result.stderr:
                    self.tool_output.insert(tk.END, f"错误信息:\n{result.stderr}\n")

                messagebox.showinfo("成功", "索引重建完成")

        except subprocess.TimeoutExpired:
            messagebox.showerror("错误", "重建索引超时")
        except Exception as e:
            error_msg = f"重建索引失败: {e}"
            self.tool_output.insert(tk.END, f"{error_msg}\n")
            messagebox.showerror("错误", error_msg)

    def system_diagnosis(self):
        """系统诊断"""
        try:
            self.tool_output.insert(tk.END, "正在进行系统诊断...\n")

            # 检查 Python 环境
            python_version = sys.version
            self.tool_output.insert(tk.END, f"Python 版本: {python_version}\n")

            # 检查依赖包
            try:
                import automem
                self.tool_output.insert(tk.END, "✓ AutoMem 模块正常\n")
            except ImportError:
                self.tool_output.insert(tk.END, "✗ AutoMem 模块未安装\n")

            # 检查配置文件
            if os.path.exists(self.config_file):
                self.tool_output.insert(tk.END, f"✓ 配置文件存在: {self.config_file}\n")
                try:
                    with open(self.config_file, 'r') as f:
                        yaml.safe_load(f)
                    self.tool_output.insert(tk.END, "✓ 配置文件格式正确\n")
                except:
                    self.tool_output.insert(tk.END, "✗ 配置文件格式错误\n")
            else:
                self.tool_output.insert(tk.END, f"✗ 配置文件不存在: {self.config_file}\n")

            # 检查数据目录
            if os.path.exists(self.data_dir):
                self.tool_output.insert(tk.END, f"✓ 数据目录存在: {self.data_dir}\n")
                data_size = self.get_directory_size(self.data_dir)
                self.tool_output.insert(tk.END, f"  数据目录大小: {data_size / 1024 / 1024:.1f} MB\n")
            else:
                self.tool_output.insert(tk.END, f"✗ 数据目录不存在: {self.data_dir}\n")

            # 检查端口占用
            try:
                import socket
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                result = sock.connect_ex(('localhost', 8000))
                if result == 0:
                    self.tool_output.insert(tk.END, "✓ 端口 8000 可用\n")
                else:
                    self.tool_output.insert(tk.END, "✗ 端口 8000 被占用\n")
                sock.close()
            except:
                self.tool_output.insert(tk.END, "? 无法检查端口状态\n")

            self.tool_output.insert(tk.END, "系统诊断完成\n")

        except Exception as e:
            error_msg = f"系统诊断失败: {e}"
            self.tool_output.insert(tk.END, f"{error_msg}\n")

    # ==================== Claude Desktop 集成方法 ====================

    def generate_claude_config(self):
        """生成 Claude Desktop 配置"""
        try:
            current_dir = os.getcwd().replace('\\', '\\\\')
            python_path = sys.executable.replace('\\', '\\\\')
            config_path = os.path.abspath(self.config_file).replace('\\', '\\\\')

            claude_config = {
                "mcpServers": {
                    "automem": {
                        "command": python_path,
                        "args": ["-m", "automem.cli", "serve", "--stdio"],
                        "env": {
                            "AUTOMEM_CONFIG": config_path,
                            "AUTOMEM_DATA_DIR": os.path.abspath(self.data_dir).replace('\\', '\\\\')
                        }
                    }
                }
            }

            config_text = json.dumps(claude_config, indent=2, ensure_ascii=False)

            # 显示配置
            config_window = ctk.CTkToplevel(self.root)
            config_window.title("Claude Desktop 配置")
            config_window.geometry("600x400")

            ctk.CTkLabel(config_window, text="复制以下配置到 Claude Desktop 配置文件中：").pack(pady=10)

            config_textbox = ctk.CTkTextbox(config_window, height=300)
            config_textbox.pack(fill="both", expand=True, padx=20, pady=10)
            config_textbox.insert("1.0", config_text)

            def copy_config():
                self.root.clipboard_clear()
                self.root.clipboard_append(config_text)
                messagebox.showinfo("成功", "配置已复制到剪贴板")

            ctk.CTkButton(config_window, text="复制到剪贴板", command=copy_config).pack(pady=10)

        except Exception as e:
            messagebox.showerror("错误", f"生成配置失败: {e}")

    def open_claude_config(self):
        """打开 Claude Desktop 配置文件"""
        try:
            config_path = os.path.expandvars(r"%APPDATA%\Claude\claude_desktop_config.json")
            if os.path.exists(config_path):
                os.startfile(config_path)
            else:
                messagebox.showwarning("警告", f"Claude Desktop 配置文件不存在:\n{config_path}")
        except Exception as e:
            messagebox.showerror("错误", f"打开配置文件失败: {e}")

    def test_claude_connection(self):
        """测试 Claude 连接"""
        try:
            self.tool_output.insert(tk.END, "正在测试 Claude 连接...\n")

            # 这里可以实现测试连接的逻辑
            # 例如启动一个临时的 MCP 服务器并测试

            self.tool_output.insert(tk.END, "连接测试功能开发中...\n")
            messagebox.showinfo("信息", "连接测试功能开发中")

        except Exception as e:
            error_msg = f"测试连接失败: {e}"
            self.tool_output.insert(tk.END, f"{error_msg}\n")
            messagebox.showerror("错误", error_msg)


def main():
    """主函数"""
    try:
        # 检查依赖
        import customtkinter
        import psutil
        import yaml
    except ImportError as e:
        print(f"缺少依赖包: {e}")
        print("请安装依赖包: pip install customtkinter psutil PyYAML")
        input("按回车键退出...")
        return

    # 创建并运行应用
    app = AutoMemManager()
    app.root.mainloop()


if __name__ == "__main__":
    main()
