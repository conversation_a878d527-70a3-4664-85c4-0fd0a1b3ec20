# 🚀 AutoMem 快速启动指南

## 🎯 项目已就绪！

恭喜！AutoMem 智能记忆管理系统已经完全配置好，现在可以正常使用了！

## 📋 当前状态

✅ **Python 3.13.5** - 已安装  
✅ **所有依赖包** - 已安装（118个包）  
✅ **AutoMem 模块** - 已修复并安装  
✅ **GUI 管理器** - 正在运行  
✅ **简化服务器** - 已创建并测试  

## 🎮 三种使用方式

### 方式一：GUI 管理器（推荐新手）

```bash
# 启动图形界面管理器
python start_gui.py
```

**功能**：
- 🎛️ 可视化服务管理
- 📊 实时状态监控
- ⚙️ 配置文件编辑
- 📋 日志查看
- 🔧 系统工具

**使用步骤**：
1. 运行上述命令
2. 在 GUI 中点击"启动服务"
3. 选择"简化模式"（快速启动）
4. 服务启动成功！

### 方式二：简化服务器（推荐测试）

```bash
# 直接启动简化服务器
python simple_server.py
```

**特点**：
- ⚡ 秒级启动
- 💾 基本记忆功能
- 🔍 关键词搜索
- 📝 MCP 协议支持

### 方式三：完整服务器（高级用户）

```bash
# 启动完整功能服务器（首次较慢）
python -m automem.cli serve --stdio
```

**特点**：
- 🧠 AI 语义搜索
- 🏷️ 自动分类标签
- 📊 智能重要性评分
- 🔗 上下文关联

## 🔌 配置 Claude Desktop

### 自动配置（推荐）

1. **运行快速启动脚本**：
   ```bash
   quick_start.bat
   ```

2. **选择选项 3** - 配置 Claude Desktop

3. **按照提示操作**

### 手动配置

1. **找到 Claude Desktop 配置文件**：
   ```
   %APPDATA%\Claude\claude_desktop_config.json
   ```

2. **复制以下配置**：
   ```json
   {
     "mcpServers": {
       "automem-simple": {
         "command": "python",
         "args": ["simple_server.py"],
         "cwd": "D:\\Users\\Administrator\\Desktop\\项目\\AutoMem",
         "env": {
           "PYTHONPATH": "D:\\Users\\Administrator\\Desktop\\项目\\AutoMem"
         }
       }
     }
   }
   ```

3. **重启 Claude Desktop**

## 🧪 测试功能

### 在 Claude 中测试

启动 Claude Desktop 后，尝试以下命令：

```
请帮我存储一个记忆：今天成功启动了 AutoMem 智能记忆系统！
```

```
搜索关于 AutoMem 的记忆
```

### 预期结果

- ✅ 记忆成功存储
- ✅ 搜索返回相关结果
- ✅ 显示记忆ID和时间戳

## 🔧 故障排除

### 问题1：GUI 启动失败
**解决方案**：
```bash
# 检查依赖
pip install customtkinter

# 重新启动
python start_gui.py
```

### 问题2：服务启动超时
**解决方案**：
- 选择"简化模式"而不是"完整模式"
- 简化模式启动只需几秒钟

### 问题3：Claude Desktop 连接失败
**解决方案**：
1. 确保路径正确（检查 `cwd` 路径）
2. 确保 Python 在系统 PATH 中
3. 重启 Claude Desktop

## 📊 性能对比

| 模式 | 启动时间 | 内存占用 | 功能 |
|------|----------|----------|------|
| 简化模式 | 2-3秒 | ~50MB | 基本记忆存储/搜索 |
| 完整模式 | 30-60秒 | ~500MB | AI语义搜索+分类 |

## 🎉 成功标志

当您看到以下信息时，说明系统运行正常：

### GUI 管理器
- 窗口正常显示
- 状态显示"服务运行中"（绿色）

### 简化服务器
```
INFO:__main__:🚀 AutoMem 简化服务器启动成功
INFO:__main__:📝 提供基本的记忆存储和检索功能
```

### Claude Desktop
- 可以成功存储和搜索记忆
- 返回格式化的结果

## 🚀 下一步

1. **熟悉基本功能**：先使用简化模式
2. **测试 Claude 集成**：验证 MCP 连接
3. **尝试完整模式**：体验 AI 功能
4. **自定义配置**：根据需要调整设置

---

**🎊 恭喜！您的 AutoMem 智能记忆管理系统已经完全就绪！**
